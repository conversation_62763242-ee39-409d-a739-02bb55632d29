2025-06-03 21:40:33 - services.trading_strategy - ERROR - Error calculating stop loss for LAYER/USDT: 'SMA_200'
2025-06-03 21:40:35 - services.trading_strategy - ERROR - Error calculating targets for LAYER/USDT: 'SMA_200'
2025-06-03 21:40:38 - services.trading_strategy - ERROR - Error calculating stop loss for LAYER/USDT: 'SMA_200'
2025-06-03 21:40:43 - services.trading_strategy - ERROR - Error calculating stop loss for LAYER/USDT: 'SMA_200'
2025-06-03 21:40:47 - services.trading_strategy - ERROR - Error calculating targets for LAYER/USDT: 'SMA_200'
2025-06-03 21:40:50 - services.trading_strategy - ERROR - Error calculating stop loss for LAYER/USDT: 'SMA_200'
2025-06-03 21:41:41 - services.trading_strategy - ERROR - Error calculating stop loss for KMNO/USDT: 'SMA_200'
2025-06-03 21:41:44 - services.trading_strategy - ERROR - Error calculating targets for KMNO/USDT: 'SMA_200'
2025-06-03 21:41:47 - services.trading_strategy - ERROR - Error calculating stop loss for KMNO/USDT: 'SMA_200'
2025-06-03 21:42:29 - services.trading_strategy - ERROR - Error calculating stop loss for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:42:31 - services.trading_strategy - ERROR - Error calculating targets for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:42:34 - services.trading_strategy - ERROR - Error calculating stop loss for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:42:46 - services.trading_strategy - ERROR - Error calculating stop loss for BMT/USDT: 'SMA_200'
2025-06-03 21:42:49 - services.trading_strategy - ERROR - Error calculating targets for BMT/USDT: 'SMA_200'
2025-06-03 21:42:51 - services.trading_strategy - ERROR - Error calculating stop loss for BMT/USDT: 'SMA_200'
2025-06-03 21:43:07 - services.trading_strategy - ERROR - Error calculating stop loss for HAEDAL/USDT: 'SMA_200'
2025-06-03 21:43:10 - services.trading_strategy - ERROR - Error calculating targets for HAEDAL/USDT: 'SMA_200'
2025-06-03 21:43:13 - services.trading_strategy - ERROR - Error calculating stop loss for HAEDAL/USDT: 'SMA_200'
2025-06-03 21:43:36 - __main__ - ERROR - Error in trade checker: cannot access local variable 'add_waiting_trade' where it is not associated with a value
2025-06-03 21:45:05 - services.trading_strategy - ERROR - Error calculating stop loss for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:45:08 - services.trading_strategy - ERROR - Error calculating targets for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:45:10 - services.trading_strategy - ERROR - Error calculating stop loss for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:45:16 - services.trading_strategy - ERROR - Error calculating stop loss for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:45:19 - services.trading_strategy - ERROR - Error calculating targets for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:45:22 - services.trading_strategy - ERROR - Error calculating stop loss for BROCCOLI714/USDT: 'SMA_200'
2025-06-03 21:45:30 - services.trading_strategy - ERROR - Error calculating stop loss for TST/USDT: 'SMA_200'
2025-06-03 21:45:33 - services.trading_strategy - ERROR - Error calculating targets for TST/USDT: 'SMA_200'
2025-06-03 21:45:35 - services.trading_strategy - ERROR - Error calculating stop loss for TST/USDT: 'SMA_200'
2025-06-03 21:45:41 - services.trading_strategy - ERROR - Error calculating stop loss for TST/USDT: 'SMA_200'
2025-06-03 21:45:44 - services.trading_strategy - ERROR - Error calculating targets for TST/USDT: 'SMA_200'
2025-06-03 21:45:46 - services.trading_strategy - ERROR - Error calculating stop loss for TST/USDT: 'SMA_200'
2025-06-03 21:46:18 - services.trading_strategy - ERROR - Error calculating stop loss for KMNO/USDT: 'SMA_200'
2025-06-03 21:46:21 - services.trading_strategy - ERROR - Error calculating targets for KMNO/USDT: 'SMA_200'
2025-06-03 21:46:24 - services.trading_strategy - ERROR - Error calculating stop loss for KMNO/USDT: 'SMA_200'
2025-06-03 21:46:38 - services.trading_strategy - ERROR - Error calculating stop loss for HAEDAL/USDT: 'SMA_200'
2025-06-03 21:46:41 - services.trading_strategy - ERROR - Error calculating targets for HAEDAL/USDT: 'SMA_200'
2025-06-03 21:46:43 - services.trading_strategy - ERROR - Error calculating stop loss for HAEDAL/USDT: 'SMA_200'
2025-06-03 21:47:13 - services.trading_strategy - ERROR - Error calculating stop loss for ARB/USDT: 'SMA_200'
2025-06-03 21:47:17 - services.trading_strategy - ERROR - Error calculating targets for ARB/USDT: 'SMA_200'
2025-06-03 21:47:19 - services.trading_strategy - ERROR - Error calculating stop loss for ARB/USDT: 'SMA_200'
2025-06-03 21:47:30 - services.trading_strategy - ERROR - Error calculating stop loss for BMT/USDT: 'SMA_200'
2025-06-03 21:47:33 - services.trading_strategy - ERROR - Error calculating targets for BMT/USDT: 'SMA_200'
2025-06-03 21:47:36 - services.trading_strategy - ERROR - Error calculating stop loss for BMT/USDT: 'SMA_200'
2025-06-03 21:47:43 - services.trading_strategy - ERROR - Error calculating stop loss for LAYER/USDT: 'SMA_200'
2025-06-03 21:47:46 - services.trading_strategy - ERROR - Error calculating targets for LAYER/USDT: 'SMA_200'
2025-06-03 21:47:48 - services.trading_strategy - ERROR - Error calculating stop loss for LAYER/USDT: 'SMA_200'
2025-06-03 21:48:15 - services.trading_strategy - ERROR - Error calculating stop loss for KAIA/USDT: 'SMA_200'
2025-06-03 21:48:18 - services.trading_strategy - ERROR - Error calculating targets for KAIA/USDT: 'SMA_200'
2025-06-03 21:48:20 - services.trading_strategy - ERROR - Error calculating stop loss for KAIA/USDT: 'SMA_200'
2025-06-03 21:48:56 - services.trading_strategy - ERROR - Error calculating stop loss for INIT/USDT: 'SMA_200'
2025-06-03 21:48:59 - services.trading_strategy - ERROR - Error calculating targets for INIT/USDT: 'SMA_200'
2025-06-03 21:49:02 - services.trading_strategy - ERROR - Error calculating stop loss for INIT/USDT: 'SMA_200'
2025-06-03 21:49:32 - __main__ - CRITICAL - Error during error recovery: cannot access local variable 'add_waiting_trade' where it is not associated with a value
