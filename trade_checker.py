"""
Trade checker script for finding high-quality trading opportunities.
This runs independently from the Telegram bot to ensure responsiveness.
"""
import os
import sys
import time
import uuid
import asyncio
import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Set up logging
from config.logging_config_consolidated import configure_logging
logger = configure_logging()
logger = logger.getChild(__name__)

# Import services
from services.binance_service import ExchangeService
from services.trading_strategy import TradingStrategy
from services.signal_service import TradeSignal

# Import shared data manager
from utils.shared_data_manager import (
    update_bot_status, get_pending_commands, mark_command_processed,
    load_shared_data
)

# Import terminal display for local monitoring
from utils.terminal_display_consolidated import display_terminal

class TradeChecker:
    """Service for checking trades and finding high-quality opportunities."""

    def __init__(self):
        """Initialize the trade checker."""
        self.exchange = ExchangeService()
        self.trading_strategy = TradingStrategy(self.exchange)
        self.running = False

        # Trade statistics
        self.trade_stats = {
            'success_spot': 0,
            'success_future': 0,
            'failed_spot': 0,
            'failed_future': 0
        }

        # Current state
        self.current_pair = ""
        self.current_market = ""
        self.current_conditions = {}

        # Initialize idle mode variables
        self.idle_mode = False
        self.idle_market_type = None
        self.idle_user_id = None

        # Simple trade storage - just store sent trades in JSON
        self.sent_trades = []
        self.trades_file = "sent_trades.json"

        # Trade quality thresholds
        self.min_quality_score = 0.65  # Minimum quality score (0-1) for a trade to be considered (more realistic)
        self.min_wait_time = 60  # Minimum time (seconds) to wait between finding trades
        self.last_trade_time = datetime.now() - timedelta(seconds=self.min_wait_time)

        # Load existing data
        self._load_existing_data()

    def _load_existing_data(self):
        """Load existing sent trades from JSON file."""
        try:
            import os
            if os.path.exists(self.trades_file):
                with open(self.trades_file, 'r') as f:
                    self.sent_trades = json.load(f)
                logger.info(f"Loaded {len(self.sent_trades)} previously sent trades")
            else:
                self.sent_trades = []
                logger.info("No previous trades file found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading trades file: {e}")
            self.sent_trades = []

    def _save_trade_to_json(self, trade_data):
        """Save a trade to the JSON file."""
        try:
            # Add timestamp
            trade_data["sent_at"] = datetime.now().isoformat()

            # Add to list
            self.sent_trades.append(trade_data)

            # Keep only last 100 trades to prevent file from growing too large
            if len(self.sent_trades) > 100:
                self.sent_trades = self.sent_trades[-100:]

            # Save to file
            with open(self.trades_file, 'w') as f:
                json.dump(self.sent_trades, f, indent=2)

            logger.info(f"Saved trade to {self.trades_file}: {trade_data['symbol']} {trade_data['direction']}")
        except Exception as e:
            logger.error(f"Error saving trade to JSON: {e}")

    async def start(self):
        """Start the trade checker."""
        if self.running:
            logger.warning("Trade checker already running")
            return

        self.running = True
        logger.info("Starting trade checker")

        # Set idle mode to true by default - only check trades when requested
        self.idle_mode = True

        # Update bot status
        update_bot_status({
            "is_running": True,
            "current_pair": "",
            "current_market": "",
            "current_conditions": {"status": "Idle - Waiting for trade requests"}
        })

        # Set a flag to prevent recursive error handling
        error_recovery_in_progress = False

        while self.running:
            try:
                # Process any pending commands
                await self._process_commands()

                # If in idle mode, just update status and sleep
                if self.idle_mode:
                    # Just sleep and wait for commands
                    await asyncio.sleep(1)
                    continue

                # Normal operation mode - simplified
                # Just sleep since we only process on-demand requests now
                await asyncio.sleep(5)

                # Reset error recovery flag after successful execution
                error_recovery_in_progress = False

                # Sleep for a while to reduce system load
                await asyncio.sleep(5)  # Check every 5 seconds
            except Exception as e:
                # Prevent recursive error handling
                if error_recovery_in_progress:
                    logger.critical(f"Error during error recovery: {e}")
                    # Force a longer sleep to break potential recursion
                    await asyncio.sleep(300)  # 5 minutes
                else:
                    error_recovery_in_progress = True
                    logger.error(f"Error in trade checker: {e}")
                    await asyncio.sleep(60)  # Sleep and retry

    def stop(self):
        """Stop the trade checker."""
        self.running = False
        logger.info("Stopping trade checker")

        # Update bot status
        update_bot_status({
            "is_running": False
        })

    async def _process_commands(self):
        """Process any pending commands."""
        commands = get_pending_commands()

        for command in commands:
            command_type = command.get("type")
            command_id = command.get("id")

            if command_type == "stop":
                self.stop()
            elif command_type == "restart":
                self.stop()
                await asyncio.sleep(1)
                await self.start()
            elif command_type == "clear_waiting_trades":
                # Clear sent trades history
                self.sent_trades = []
                try:
                    with open(self.trades_file, 'w') as f:
                        json.dump([], f)
                    logger.info("Cleared sent trades history")
                except Exception as e:
                    logger.error(f"Error clearing trades file: {e}")
            elif command_type == "find_trade":
                # Get market type and user ID from command
                market_type = command.get("market_type", "SPOT")
                user_id = command.get("user_id")

                # Log the request
                logger.info(f"Processing find_trade command for {market_type} market requested by user {user_id}")

                # Process the trade request immediately
                # No need to track idle mode or queue - just process each request directly

                # Update bot status
                update_bot_status({
                    "is_running": True,
                    "current_pair": "",
                    "current_market": market_type,
                    "current_conditions": {"status": f"Searching for high-quality {market_type} trade for user {user_id}..."}
                })

                # Find a high-quality trade for this user
                await self._find_specific_trade(market_type, user_id)



            # Mark command as processed
            mark_command_processed(command_id)

    async def _check_market(self):
        """Check market conditions and get viable trading pairs."""
        logger.info("Checking market conditions")

        try:
            # Update bot status
            update_bot_status({
                "current_pair": "",
                "current_market": "",
                "current_conditions": {"status": "Getting viable trading pairs..."}
            })

            # Get viable trading pairs
            viable_pairs = self.exchange.get_viable_trading_pairs()

            # Limit to top 150 tokens to avoid overload
            if len(viable_pairs) > 150:
                logger.info(f"Limiting check to top 150 tokens out of {len(viable_pairs)}")
                viable_pairs = viable_pairs[:150]

            # Update display
            display_terminal()

            return viable_pairs
        except Exception as e:
            logger.error(f"Error checking market: {e}")
            return []

    # Removed complex tracking methods - system now simplified to just send trades and store in JSON

    # Removed _check_market_pairs method - no longer needed with simplified system

    # Removed _analyze_pair method - part of old complex tracking system

    def _calculate_quality_score(self, symbol, market_type, analysis_result=None):
        """
        Calculate a smart quality score (0-1) for a trade based on technical analysis and market factors.
        Now includes analysis results for more intelligent scoring.
        """
        try:
            # Get current price
            current_price = self.trading_strategy.get_current_price(symbol)

            # 1. Technical Analysis Score (40% weight) - NEW: Based on actual analysis
            technical_score = 0.5  # Default
            if analysis_result:
                # Count how many conditions are met
                conditions_met = 0
                total_conditions = 0

                for key, value in analysis_result.items():
                    if key not in ["symbol", "market_type", "direction", "explanations", "concepts_met"]:
                        total_conditions += 1
                        if value:
                            conditions_met += 1

                # Technical score based on percentage of conditions met
                if total_conditions > 0:
                    technical_score = conditions_met / total_conditions

                # Bonus for having explanations (indicates strong patterns)
                explanations = analysis_result.get("explanations", [])
                if explanations:
                    explanation_bonus = min(0.2, len(explanations) * 0.05)  # Up to 20% bonus
                    technical_score = min(1.0, technical_score + explanation_bonus)

                # Bonus for multiple concepts met
                concepts_met = analysis_result.get("concepts_met", [])
                if len(concepts_met) >= 3:  # 3 or more concepts
                    technical_score = min(1.0, technical_score + 0.15)  # 15% bonus
                elif len(concepts_met) >= 2:  # 2 concepts
                    technical_score = min(1.0, technical_score + 0.1)   # 10% bonus

            # 2. Risk/Reward Score (25% weight) - Improved calculation
            rr_score = 0.5  # Default
            try:
                stop_loss = self.trading_strategy.calculate_stop_loss(symbol, "BUY")
                targets = self.trading_strategy.calculate_targets(symbol, "BUY")

                if targets and len(targets) > 0:
                    # Use first target for conservative R/R calculation
                    first_target = targets[0]
                    risk = abs(current_price - stop_loss)
                    reward = abs(first_target - current_price)

                    if risk > 0:
                        rr_ratio = reward / risk
                        # More generous scoring: 1.5:1 = 0.5, 2:1 = 0.67, 3:1 = 1.0
                        rr_score = min(1.0, max(0.3, (rr_ratio - 1) / 2 + 0.5))
            except Exception as e:
                logger.warning(f"Error calculating R/R score for {symbol}: {e}")

            # 3. Market Liquidity Score (20% weight) - Simplified and more generous
            liquidity_score = 0.6  # Default good score
            try:
                # Get 1h data for volume analysis
                df_1h = self.trading_strategy.exchange.get_ohlcv(symbol, "1h", limit=24)
                if df_1h is not None and not df_1h.empty:
                    avg_volume = df_1h['volume'].mean()
                    recent_volume = df_1h['volume'].iloc[-3:].mean()  # Last 3 hours

                    # Score based on recent vs average volume
                    volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
                    liquidity_score = min(1.0, max(0.4, 0.5 + (volume_ratio - 1) * 0.3))
            except Exception as e:
                logger.warning(f"Error calculating liquidity score for {symbol}: {e}")

            # 4. Volatility Score (10% weight) - Balanced approach
            volatility_score = 0.6  # Default
            try:
                df_4h = self.trading_strategy.exchange.get_ohlcv(symbol, "4h", limit=12)
                if df_4h is not None and not df_4h.empty:
                    volatility = df_4h['close'].pct_change().std()
                    # Optimal volatility range: 2-6% (gets highest score)
                    if 0.02 <= volatility <= 0.06:
                        volatility_score = 0.8
                    elif 0.01 <= volatility <= 0.08:
                        volatility_score = 0.7
                    elif volatility < 0.01:
                        volatility_score = 0.5  # Too low volatility
                    else:
                        volatility_score = 0.4  # Too high volatility
            except Exception as e:
                logger.warning(f"Error calculating volatility score for {symbol}: {e}")

            # 5. Market Type Bonus (5% weight)
            market_bonus = 0.05 if market_type == "FUTURES" else 0.0

            # Calculate final weighted score
            final_score = (
                technical_score * 0.40 +    # 40% - Most important
                rr_score * 0.25 +           # 25% - Risk management
                liquidity_score * 0.20 +    # 20% - Market conditions
                volatility_score * 0.10 +   # 10% - Price movement
                market_bonus                # 5% - Market type preference
            )

            # Ensure score is within bounds
            final_score = max(0.0, min(1.0, final_score))

            # Log detailed scoring for debugging
            logger.info(f"Quality score for {symbol}: {final_score:.3f} "
                       f"(Tech: {technical_score:.2f}, R/R: {rr_score:.2f}, "
                       f"Liquidity: {liquidity_score:.2f}, Vol: {volatility_score:.2f})")

            return final_score

        except Exception as e:
            logger.error(f"Error calculating quality score for {symbol}: {e}")
            return 0.55  # Slightly above middle for fallback

    # Removed _check_active_trades and _update_shared_data methods - part of old complex tracking system

    def _is_stable_coin_pair(self, pair):
        """Check if a pair is a stable coin pair that should be skipped."""
        stable_coins = ["USDC", "BUSD", "TUSD", "DAI", "USDT", "FDUSD", "USDK", "USDP", "USDD"]

        # Extract the base currency from the pair (e.g., "BTC" from "BTC/USDT")
        if "/" in pair:
            base = pair.split("/")[0]
            quote = pair.split("/")[1]

            # Check if both base and quote are stable coins
            return base in stable_coins and quote in stable_coins

        return False

    def _generate_trade_explanation(self, trade_data):
        """Generate a comprehensive educational explanation for why this trade was selected."""
        symbol = trade_data["symbol"]
        direction = trade_data["direction"]
        market_type = trade_data.get("market_type", "SPOT")

        # Use real explanations from analysis if available
        real_explanations = trade_data.get("explanations", [])
        concepts_met = trade_data.get("concepts_met", [])
        conditions = trade_data.get("conditions", {})

        explanation = []

        # Add educational header
        explanation.append("📚 *EDUCATIONAL TRADE BREAKDOWN*")
        explanation.append("Learn why this trade was selected using professional analysis:\n")

        # 1. DETAILED TECHNICAL ANALYSIS FINDINGS
        if real_explanations:
            explanation.append("🔍 *TECHNICAL ANALYSIS FINDINGS:*")
            for i, exp in enumerate(real_explanations, 1):
                # Format each explanation with numbering and educational context
                clean_exp = exp.strip()
                if not clean_exp.startswith("•"):
                    explanation.append(f"{i}. {clean_exp}")
                else:
                    explanation.append(f"{i}. {clean_exp[1:].strip()}")
            explanation.append("")

        # 2. TRADING CONCEPTS EDUCATION
        if concepts_met:
            explanation.append("📖 *TRADING CONCEPTS APPLIED:*")
            concept_descriptions = {
                "ICT": "Institutional Candle Theory - Smart money concepts and liquidity analysis",
                "SMC": "Smart Money Concepts - Understanding institutional trading behavior",
                "PA": "Price Action - Reading market sentiment through candlestick patterns",
                "S/R": "Support & Resistance - Key price levels where market reacts",
                "Pattern Recognition": "Chart patterns indicating potential price movements"
            }

            for concept in concepts_met:
                description = concept_descriptions.get(concept, "Advanced technical analysis method")
                explanation.append(f"✓ **{concept}**: {description}")
            explanation.append("")

        # 3. DIRECTIONAL BIAS WITH EDUCATION
        direction_display = self._get_direction_display(direction, market_type)
        if direction in ["BUY", "LONG"]:
            explanation.append(f"📈 *WHY {direction_display} POSITION:*")
            explanation.append("• **Bullish Market Structure**: Price showing higher highs and higher lows")
            explanation.append("• **Demand Zone Identified**: Strong buying interest at current levels")
            explanation.append("• **Momentum Confirmation**: Multiple timeframes align bullish")
            explanation.append("• **Risk Management**: Stop loss placed below key support level")
        elif direction in ["SELL", "SHORT"]:
            explanation.append(f"📉 *WHY {direction_display} POSITION:*")
            explanation.append("• **Bearish Market Structure**: Price showing lower highs and lower lows")
            explanation.append("• **Supply Zone Identified**: Strong selling pressure at current levels")
            explanation.append("• **Momentum Confirmation**: Multiple timeframes align bearish")
            explanation.append("• **Risk Management**: Stop loss placed above key resistance level")
        explanation.append("")

        # 4. MARKET CONTEXT & EDUCATION
        explanation.append("🌍 *MARKET CONTEXT:*")

        # Symbol-specific education
        base_currency = symbol.split("/")[0] if "/" in symbol else symbol
        if base_currency.upper() in ["BTC", "BITCOIN"]:
            explanation.append("• **Bitcoin Analysis**: Market leader, often sets direction for entire crypto market")
            explanation.append("• **Institutional Interest**: High correlation with traditional markets during risk-off periods")
        elif base_currency.upper() in ["ETH", "ETHEREUM"]:
            explanation.append("• **Ethereum Analysis**: Smart contract platform with strong DeFi ecosystem")
            explanation.append("• **Network Activity**: Price often correlates with on-chain activity and gas fees")
        elif base_currency.upper() in ["BNB"]:
            explanation.append("• **Binance Coin**: Exchange token with utility in Binance ecosystem")
            explanation.append("• **Burn Mechanism**: Quarterly burns create deflationary pressure")
        else:
            explanation.append(f"• **{base_currency} Analysis**: Altcoin with specific use case and market dynamics")
            explanation.append("• **Market Correlation**: Often follows Bitcoin trend with amplified moves")

        # Add volatility and risk context
        try:
            volatility = self.trading_strategy.calculate_volatility(symbol)
            if volatility > 0.06:  # 6% volatility
                explanation.append(f"• **High Volatility** ({volatility:.1%}): Expect larger price swings, use smaller position size")
            elif volatility > 0.03:  # 3% volatility
                explanation.append(f"• **Moderate Volatility** ({volatility:.1%}): Balanced risk/reward potential")
            else:
                explanation.append(f"• **Low Volatility** ({volatility:.1%}): More stable price action, lower risk")
        except:
            explanation.append("• **Volatility**: Moderate price movement expected based on recent action")
        explanation.append("")

        # 5. RISK MANAGEMENT EDUCATION
        explanation.append("⚠️ *RISK MANAGEMENT LESSONS:*")
        explanation.append("• **Position Sizing**: Never risk more than 1-2% of your total capital")
        explanation.append("• **Stop Loss**: Always set before entering - protects against major losses")
        explanation.append("• **Take Profits**: Consider taking partial profits at each target level")
        explanation.append("• **Market Conditions**: Be aware of major news events that could affect price")

        if market_type == "FUTURES":
            explanation.append("• **Leverage Warning**: Higher leverage = higher risk, start with low leverage")
            explanation.append("• **Funding Rates**: Monitor funding costs for longer-term positions")
        explanation.append("")

        # 6. LEARNING OBJECTIVES
        explanation.append("🎯 *WHAT YOU CAN LEARN:*")
        explanation.append("• Study how multiple technical indicators confirm each other")
        explanation.append("• Observe how price reacts at the identified support/resistance levels")
        explanation.append("• Notice the relationship between volume and price movement")
        explanation.append("• Track how market structure develops over time")

        # If no explanations were provided, add educational generic ones
        if not real_explanations and not concepts_met:
            explanation.append("📊 *GENERAL ANALYSIS:*")
            explanation.append("• Multiple technical factors have aligned for this trading opportunity")
            explanation.append("• Price action shows clear directional bias with good risk/reward setup")
            explanation.append("• Market structure supports the anticipated price movement")

        # Return the formatted explanation
        return "\n".join(explanation)

    def _get_direction_display(self, direction, market_type):
        """Get the appropriate direction display based on market type."""
        if market_type == "FUTURES":
            return "LONG" if direction == "BUY" else "SHORT"
        else:
            return direction

    async def _find_specific_trade(self, market_type, user_id):
        """Find a high-quality trade for a specific user and market type."""
        from telegram import Bot
        from config.settings import BOT_TOKEN

        logger.info(f"Finding a high-quality {market_type} trade for user {user_id}")

        # Create a bot instance to send messages
        bot = Bot(token=BOT_TOKEN)

        # Get viable trading pairs
        viable_pairs = self.exchange.get_viable_trading_pairs()

        # Limit to top 150 tokens to avoid overload
        if len(viable_pairs) > 150:
            viable_pairs = viable_pairs[:150]

        # Filter out stable coin pairs
        filtered_pairs = [p for p in viable_pairs if not self._is_stable_coin_pair(p)]

        # Shuffle pairs to avoid always checking the same ones first
        random.shuffle(filtered_pairs)

        # Send initial progress message that we'll update
        progress_message = None
        try:
            progress_message = await bot.send_message(
                chat_id=user_id,
                text=f"🔍 *TRADE ANALYSIS IN PROGRESS*\n\n"
                     f"Analyzing {len(filtered_pairs)} trading pairs for high-quality {market_type} opportunities...\n\n"
                     f"Progress: 0%\n"
                     f"Current pair: Initializing...",
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Error sending initial progress message to user {user_id}: {e}")
            # Try to continue even if we couldn't send the initial message

        # Track the best trade found
        best_trade = None
        best_quality = 0

        # Process pairs in smaller batches with progress updates
        batch_size = 10
        total_batches = (len(filtered_pairs) + batch_size - 1) // batch_size

        # For slower, more visible updates
        update_frequency = 2  # Update every 2 batches

        for batch_num, i in enumerate(range(0, len(filtered_pairs), batch_size)):
            batch = filtered_pairs[i:i+batch_size]

            # Update progress message every few batches
            if batch_num % update_frequency == 0:
                progress = (batch_num / total_batches) * 100
                try:
                    if progress_message:
                        await bot.edit_message_text(
                            chat_id=user_id,
                            message_id=progress_message.message_id,
                            text=f"🔍 *TRADE ANALYSIS IN PROGRESS*\n\n"
                                 f"Analyzing {len(filtered_pairs)} trading pairs for high-quality {market_type} opportunities...\n\n"
                                 f"Progress: {progress:.1f}%\n"
                                 f"Currently checking: {', '.join(batch)}",
                            parse_mode='Markdown'
                        )
                except Exception as e:
                    logger.error(f"Error updating progress message for user {user_id}: {e}")

            # Analyze each pair in the batch
            for symbol in batch:
                # Update current pair and market
                self.current_pair = symbol
                self.current_market = market_type

                # Update bot status
                update_bot_status({
                    "current_pair": symbol,
                    "current_market": market_type,
                    "current_conditions": {"status": f"Analyzing {symbol} for {market_type} market..."}
                })

                # Analyze the pair
                try:
                    # Analyze the symbol with specific market type
                    analysis_result = self.trading_strategy.analyze_symbol(symbol, market_type)

                    # Skip if no valid analysis
                    if analysis_result is None:
                        continue

                    # Check if all conditions are met and we have a valid direction
                    all_conditions_met = True
                    for key, value in analysis_result.items():
                        if key not in ["symbol", "market_type", "direction", "explanations", "concepts_met"] and not value:
                            all_conditions_met = False
                            break

                    # Get the signal direction
                    signal_direction = analysis_result.get("direction", "NONE")

                    if all_conditions_met and signal_direction in ["BUY", "SELL"]:
                        # Calculate quality score with analysis results
                        quality_score = self._calculate_quality_score(symbol, market_type, analysis_result)

                        # Check if this is the best trade so far
                        if quality_score > best_quality:
                            best_quality = quality_score

                            # Create trade details
                            best_trade = {
                                "symbol": symbol,
                                "market_type": market_type,
                                "direction": signal_direction,  # Use determined direction
                                "entry_price": self.trading_strategy.get_current_price(symbol),
                                "stop_loss": self.trading_strategy.calculate_stop_loss(symbol, signal_direction),
                                "targets": self.trading_strategy.calculate_targets(symbol, signal_direction),
                                "quality_score": quality_score,
                                "found_at": datetime.now().isoformat(),
                                "conditions": {k: v for k, v in analysis_result.items() if k not in ["symbol", "market_type", "direction", "explanations", "concepts_met"]},
                                "explanations": analysis_result.get("explanations", []),
                                "concepts_met": analysis_result.get("concepts_met", [])
                            }

                            logger.info(f"Found potential {market_type} trade: {symbol} (Score: {quality_score:.2f})")
                except Exception as e:
                    logger.error(f"Error analyzing {symbol} for {market_type}: {e}")

            # Add a small delay between batches
            await asyncio.sleep(1.0)

        # Delete the progress message
        try:
            if progress_message:
                await bot.delete_message(chat_id=user_id, message_id=progress_message.message_id)
        except Exception as e:
            logger.error(f"Error deleting progress message: {e}")

        # Check if we found a good trade
        if best_trade and best_quality >= self.min_quality_score:
            logger.info(f"Found high-quality {market_type} trade for user {user_id}: {best_trade['symbol']} (Score: {best_quality:.2f})")

            # Add to waiting trades
            if market_type == "SPOT":
                self.waiting_spot.append(best_trade)
            else:
                self.waiting_future.append(best_trade)

            # Add to shared data
            add_waiting_trade(best_trade, market_type)

            # Format trade message
            entry_price = best_trade["entry_price"]
            stop_loss = best_trade["stop_loss"]
            targets = best_trade["targets"]

            # Calculate risk/reward ratios
            risk = abs(entry_price - stop_loss)
            risk_reward_ratios = [abs(target - entry_price) / risk for target in targets]

            # Format prices with appropriate precision
            if entry_price < 0.1:
                price_format = "{:.8f}"
            elif entry_price < 1:
                price_format = "{:.6f}"
            elif entry_price < 100:
                price_format = "{:.4f}"
            else:
                price_format = "{:.2f}"

            # Generate detailed trade explanation based on conditions
            explanation = self._generate_trade_explanation(best_trade)

            # Get proper direction display
            direction_display = self._get_direction_display(best_trade['direction'], market_type)

            # Create enhanced message with educational elements
            message = (
                f"🎯 *HIGH-QUALITY {market_type} TRADE SIGNAL*\n\n"
                f"📊 **TRADE DETAILS:**\n"
                f"*Symbol:* {best_trade['symbol']}\n"
                f"*Direction:* {direction_display}\n"
                f"*Entry Price:* {price_format.format(entry_price)}\n"
                f"*Stop Loss:* {price_format.format(stop_loss)}\n\n"
                f"🎯 **PROFIT TARGETS:**\n"
            )

            for i, (target, ratio) in enumerate(zip(targets, risk_reward_ratios)):
                target_emoji = "🥇" if i == 0 else "🥈" if i == 1 else "🥉"
                message += f"{target_emoji} Target {i+1}: {price_format.format(target)} (R/R: {ratio:.2f})\n"

            message += f"\n⭐ *Quality Score:* {best_quality:.2f}/1.0 (High Quality)\n\n"

            # Add concepts met with educational value
            concepts_met = best_trade.get("concepts_met", [])
            if concepts_met:
                message += f"📈 *Trading Concepts Confirmed:*\n"
                concept_emojis = {"ICT": "🏛️", "SMC": "🧠", "PA": "📊", "S/R": "🎯", "Pattern Recognition": "🔍"}
                for concept in concepts_met:
                    emoji = concept_emojis.get(concept, "✅")
                    message += f"{emoji} {concept}\n"
                message += "\n"

            # Add risk management section
            risk_percent = abs((entry_price - stop_loss) / entry_price * 100)
            reward_percent = abs((targets[0] - entry_price) / entry_price * 100) if targets else 0

            message += f"⚠️ *Risk Management:*\n"
            message += f"• Risk: {risk_percent:.1f}% | Reward: {reward_percent:.1f}%\n"
            message += f"• Position Size: 1-2% of capital maximum\n"
            message += f"• Always set stop loss before entering\n\n"

            # Add volume details
            if best_trade["conditions"].get("Volume condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Increasing buy volume confirms bullish momentum\n"
                else:
                    message += f"• Increasing sell volume confirms bearish pressure\n"

            # Add momentum details
            if best_trade["conditions"].get("Momentum condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Strong bullish momentum on indicators\n"
                else:
                    message += f"• Strong bearish momentum on indicators\n"

            # Add the detailed explanation
            message += f"\n*Technical Analysis Details:*\n{explanation}\n"

            # Add specific guidance based on market type
            if market_type == "FUTURES":
                # Calculate recommended leverage based on volatility and risk
                try:
                    volatility = self.trading_strategy.calculate_volatility(best_trade['symbol'])
                    # Lower volatility allows for higher leverage, higher volatility requires lower leverage
                    if volatility < 0.02:  # Very low volatility (<2%)
                        recommended_leverage = "5-10x"
                    elif volatility < 0.04:  # Low volatility (2-4%)
                        recommended_leverage = "3-5x"
                    elif volatility < 0.06:  # Medium volatility (4-6%)
                        recommended_leverage = "2-3x"
                    else:  # High volatility (>6%)
                        recommended_leverage = "1-2x"
                except:
                    # Default recommendation if volatility calculation fails
                    recommended_leverage = "2-5x"

                message += f"\n*Futures Trading Guidance:*\n"
                message += f"• Recommended leverage: {recommended_leverage}\n"
                message += f"• Position size: 1-2% of your capital\n"
                message += f"• Set stop loss exactly as recommended\n"
                message += f"• Consider taking partial profits at each target\n"

            message += f"\nTo accept this trade, click the button below:"

            # Create unique signal ID
            signal_id = f"{best_trade['symbol'].replace('/', '_')}_{best_trade['direction']}_{datetime.now().timestamp()}"

            # Send the trade signal to the user without any buttons
            try:
                # Extract trade details
                entry_price = best_trade.get("entry_price", 0)
                stop_loss = best_trade.get("stop_loss", 0)
                targets = best_trade.get("targets", [])

                # Determine market type
                market_type_str = market_type.upper()

                # Extract leverage recommendation for futures
                leverage = None
                if market_type_str == "FUTURES":
                    try:
                        volatility = self.trading_strategy.calculate_volatility(best_trade['symbol'])
                        if volatility < 0.02:  # Very low volatility (<2%)
                            leverage = "5-10x"
                        elif volatility < 0.04:  # Low volatility (2-4%)
                            leverage = "3-5x"
                        elif volatility < 0.06:  # Medium volatility (4-6%)
                            leverage = "2-3x"
                        else:  # High volatility (>6%)
                            leverage = "1-2x"
                    except:
                        leverage = "2-5x"

                # Create trade data
                trade_data = {
                    "signal_id": signal_id,
                    "symbol": best_trade['symbol'],
                    "direction": best_trade['direction'],
                    "entry_price": entry_price,
                    "stop_loss": stop_loss,
                    "targets": targets,
                    "leverage": leverage,
                    "market_type": market_type_str,
                    "quality_score": best_quality,
                    "user_id": user_id,
                    "timestamp": datetime.now().timestamp(),
                    "status": "waiting_entry",
                    "conditions": best_trade.get("conditions", {})
                }

                # Add trade to waiting trades
                from utils.shared_data_manager import add_waiting_trade
                add_waiting_trade(trade_data, market_type_str)

                # Record the trade for this user (counts against daily limit)
                from models.database import record_trade
                record_trade(user_id, trade_data)

                # Save trade to JSON file
                self._save_trade_to_json(trade_data)

                # Send message without buttons
                await bot.send_message(
                    chat_id=user_id,
                    text=message,
                    parse_mode='Markdown'
                )

                logger.info(f"Sent high-quality trade signal {signal_id} to user {user_id} and saved to JSON")
            except Exception as e:
                logger.error(f"Error sending trade signal to user {user_id}: {e}")
        else:
            # No good trade found
            logger.info(f"No high-quality {market_type} trade found for user {user_id}")

            try:
                # Check if we found any medium-risk trades
                medium_risk_trades = []
                for symbol in filtered_pairs:
                    # Calculate a basic quality score
                    try:
                        quality_score = self._calculate_quality_score(symbol, market_type)
                        if 0.45 <= quality_score < self.min_quality_score:
                            # This is a medium-risk trade - get proper analysis
                            try:
                                analysis_result = self.trading_strategy.analyze_symbol(symbol, market_type)
                                if analysis_result:
                                    signal_direction = analysis_result.get("direction", "BUY")
                                    if signal_direction == "NONE":
                                        signal_direction = "BUY"  # Default for medium-risk
                                else:
                                    signal_direction = "BUY"  # Default fallback
                            except:
                                signal_direction = "BUY"  # Default fallback

                            current_price = self.trading_strategy.get_current_price(symbol)
                            stop_loss = self.trading_strategy.calculate_stop_loss(symbol, signal_direction)
                            targets = self.trading_strategy.calculate_targets(symbol, signal_direction)

                            # Only include first 2 targets for medium-risk trades
                            if len(targets) > 2:
                                targets = targets[:2]

                            medium_risk_trades.append({
                                "symbol": symbol,
                                "quality_score": quality_score,
                                "entry_price": current_price,
                                "stop_loss": stop_loss,
                                "targets": targets,
                                "direction": signal_direction
                            })

                            # Only need one medium-risk trade
                            if len(medium_risk_trades) >= 1:
                                break
                    except Exception as e:
                        logger.error(f"Error calculating medium-risk trade for {symbol}: {e}")

                if medium_risk_trades:
                    # We found a medium-risk trade, offer it with warnings
                    trade = medium_risk_trades[0]
                    symbol = trade["symbol"]
                    entry_price = trade["entry_price"]
                    stop_loss = trade["stop_loss"]
                    targets = trade["targets"]
                    direction = trade.get("direction", "BUY")

                    # Format prices with appropriate precision
                    if entry_price < 0.1:
                        price_format = "{:.8f}"
                    elif entry_price < 1:
                        price_format = "{:.6f}"
                    elif entry_price < 100:
                        price_format = "{:.4f}"
                    else:
                        price_format = "{:.2f}"

                    # Generate detailed trade explanation using real analysis
                    try:
                        # Get analysis for this symbol to provide real explanations
                        analysis_result = self.trading_strategy.analyze_symbol(symbol, market_type)
                        if analysis_result:
                            real_explanations = analysis_result.get("explanations", [])
                            concepts_met = analysis_result.get("concepts_met", [])
                            signal_direction = analysis_result.get("direction", "BUY")

                            explanation = ""

                            # Add professional technical analysis section
                            if real_explanations:
                                explanation += "*Technical Analysis:*\n"
                                for exp in real_explanations:
                                    if not exp.startswith("•"):
                                        explanation += f"• {exp}\n"
                                    else:
                                        explanation += f"{exp}\n"
                                explanation += "\n"

                            # Add trading concepts section
                            if concepts_met:
                                explanation += f"*Concepts Confirmed:* {', '.join(concepts_met)}\n\n"

                            # Add directional analysis
                            if signal_direction == "BUY":
                                explanation += "*Bullish Setup Analysis:*\n"
                                explanation += "• Multiple bullish confluences identified\n"
                                explanation += "• Risk/reward ratio supports long position\n"
                                explanation += "• Entry timing based on technical structure\n\n"
                            elif signal_direction == "SELL":
                                explanation += "*Bearish Setup Analysis:*\n"
                                explanation += "• Multiple bearish confluences identified\n"
                                explanation += "• Risk/reward ratio supports short position\n"
                                explanation += "• Entry timing based on technical structure\n\n"
                        else:
                            explanation = "*Technical Analysis:*\n• Multiple technical factors aligned for this opportunity\n\n"
                    except Exception as e:
                        logger.error(f"Error generating detailed explanation: {e}")
                        explanation = "*Technical Analysis:*\n• Multiple technical factors aligned for this opportunity\n\n"

                    # Add professional market context
                    explanation += "*Market Context:*\n"
                    if "BTC" in symbol:
                        explanation += "• Bitcoin - Dominant cryptocurrency with high liquidity and institutional adoption\n"
                    elif "ETH" in symbol:
                        explanation += "• Ethereum - Leading smart contract platform with strong DeFi ecosystem\n"
                    elif "BNB" in symbol:
                        explanation += "• Binance Coin - Exchange token with utility and burn mechanics\n"
                    else:
                        explanation += f"• {symbol.split('/')[0]} - Altcoin with specific market dynamics\n"

                    # Add volatility and risk assessment
                    try:
                        volatility = self.trading_strategy.calculate_volatility(symbol)
                        if volatility > 0.06:  # 6% volatility
                            explanation += f"• High volatility asset ({volatility:.1%}) - Higher profit potential but increased risk\n"
                        elif volatility < 0.03:  # 3% volatility
                            explanation += f"• Lower volatility asset ({volatility:.1%}) - More stable price action\n"
                        else:
                            explanation += f"• Moderate volatility ({volatility:.1%}) - Balanced risk/reward profile\n"
                    except:
                        explanation += "• Moderate volatility expected based on recent price action\n"

                    # Add professional risk assessment
                    explanation += "\n*Risk Assessment:*\n"
                    explanation += "• Medium-risk setup with moderate probability of success\n"
                    explanation += "• Recommended for traders with intermediate experience\n"
                    explanation += "• Strict risk management required\n"

                    # Get proper direction display for medium-risk trades
                    direction_display = self._get_direction_display(direction, market_type)

                    message = (
                        f"⚠️ *MEDIUM-RISK {market_type} TRADE AVAILABLE*\n\n"
                        f"📊 **TRADE DETAILS:**\n"
                        f"*Symbol:* {symbol}\n"
                        f"*Direction:* {direction_display}\n"
                        f"*Entry Price:* {price_format.format(entry_price)}\n"
                        f"*Stop Loss:* {price_format.format(stop_loss)}\n\n"
                        f"🎯 **LIMITED TARGETS:**\n"
                    )

                    for i, target in enumerate(targets):
                        message += f"{i+1}. {price_format.format(target)}\n"

                    # Add the explanation to the message
                    message += f"\n*Why This Trade Was Selected:*\n"
                    message += explanation

                    # Add leverage recommendation for futures trades
                    if market_type == "FUTURES":
                        try:
                            volatility = self.trading_strategy.calculate_volatility(symbol)
                            # For medium-risk trades, recommend lower leverage
                            if volatility < 0.02:  # Very low volatility (<2%)
                                recommended_leverage = "3-5x"
                            elif volatility < 0.04:  # Low volatility (2-4%)
                                recommended_leverage = "2-3x"
                            elif volatility < 0.06:  # Medium volatility (4-6%)
                                recommended_leverage = "1-2x"
                            else:  # High volatility (>6%)
                                recommended_leverage = "1x"
                        except:
                            # Default recommendation if volatility calculation fails
                            recommended_leverage = "1-3x"

                        message += f"\n*RISK WARNING:*\n"
                        message += f"• This is a MEDIUM-RISK trade with lower probability of success\n"
                        message += f"• Consider taking profits at Target 1 or Target 2\n"
                        message += f"• Use smaller position size (0.5-1% of capital)\n"
                        message += f"• Set strict stop loss and don't chase entry if missed\n\n"
                        message += f"*Futures Trading Guidance:*\n"
                        message += f"• Recommended leverage: {recommended_leverage}\n"
                        message += f"• Position size: 0.5-1% of your capital\n"
                        message += f"• Set stop loss exactly as recommended\n"
                        message += f"• Consider taking partial profits at first target\n\n"
                    else:
                        message += f"\n*RISK WARNING:*\n"
                        message += f"• This is a MEDIUM-RISK trade with lower probability of success\n"
                        message += f"• Consider taking profits at Target 1 or Target 2\n"
                        message += f"• Use smaller position size (0.5-1% of capital)\n"
                        message += f"• Set strict stop loss and don't chase entry if missed\n\n"

                    # Create unique signal ID
                    signal_id = f"{symbol.replace('/', '_')}_{direction}_{datetime.now().timestamp()}"

                    # Extract trade details
                    entry_price = trade["entry_price"]
                    stop_loss = trade["stop_loss"]
                    targets = trade["targets"]

                    # Determine market type
                    market_type_str = market_type.upper()

                    # Extract leverage recommendation for futures
                    leverage = None
                    if market_type_str == "FUTURES":
                        try:
                            volatility = self.trading_strategy.calculate_volatility(symbol)
                            if volatility < 0.02:  # Very low volatility (<2%)
                                leverage = "3-5x"
                            elif volatility < 0.04:  # Low volatility (2-4%)
                                leverage = "2-3x"
                            elif volatility < 0.06:  # Medium volatility (4-6%)
                                leverage = "1-2x"
                            else:  # High volatility (>6%)
                                leverage = "1x"
                        except:
                            leverage = "1-3x"

                    # Create trade data
                    trade_data = {
                        "signal_id": signal_id,
                        "symbol": symbol,
                        "direction": direction,  # Use proper direction
                        "entry_price": entry_price,
                        "stop_loss": stop_loss,
                        "targets": targets,
                        "leverage": leverage,
                        "market_type": market_type_str,
                        "quality_score": trade["quality_score"],
                        "user_id": user_id,
                        "timestamp": datetime.now().timestamp(),
                        "status": "waiting_entry"
                    }

                    # Add trade to waiting trades
                    from utils.shared_data_manager import add_waiting_trade
                    add_waiting_trade(trade_data, market_type_str)

                    # Record the trade for this user (counts against daily limit)
                    from models.database import record_trade
                    record_trade(user_id, trade_data)

                    # Save trade to JSON file
                    self._save_trade_to_json(trade_data)

                    # Send message without buttons
                    await bot.send_message(
                        chat_id=user_id,
                        text=message,
                        parse_mode='Markdown'
                    )

                    logger.info(f"Sent medium-risk trade signal {signal_id} to user {user_id} and saved to JSON")
                else:
                    # No trades found at all
                    await bot.send_message(
                        chat_id=user_id,
                        text=f"❌ No suitable {market_type} trades found at this time.\n\n"
                             f"We analyzed {len(filtered_pairs)} trading pairs but none met our criteria.\n\n"
                             f"*Trading Guidance:*\n"
                             f"• Current market conditions are not favorable\n"
                             f"• Consider waiting for better market conditions\n"
                             f"• The best traders know when NOT to trade\n"
                             f"• Try again in a few hours when volatility or trend conditions change\n\n"
                             f"You can use /trade again later to check for new opportunities.",
                        parse_mode='Markdown'
                    )
            except Exception as e:
                logger.error(f"Error sending no-trade message to user {user_id}: {e}")

async def main():
    """Main function to run the trade checker."""
    # Create trade checker
    trade_checker = TradeChecker()

    # Set up signal handlers for graceful shutdown
    import signal

    def signal_handler(sig, frame):
        logger.info(f"Received signal {sig}, shutting down gracefully...")
        trade_checker.stop()
        logger.info("Trade checker stopped by signal")
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

    try:
        # Start trade checker
        logger.info("Starting trade checker in automated mode")
        await trade_checker.start()
    except KeyboardInterrupt:
        # Stop trade checker on keyboard interrupt
        trade_checker.stop()
        logger.info("Trade checker stopped by user")
    except Exception as e:
        # Log any other exceptions
        logger.error(f"Error in trade checker: {e}")
        trade_checker.stop()

if __name__ == "__main__":
    # Run the trade checker
    asyncio.run(main())
