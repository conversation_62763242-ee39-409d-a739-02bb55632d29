"""
Signal service for the trading bot.
"""
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field, asdict

from models.database import get_active_signals, save_active_signal, update_active_signal as update_signal_db

logger = logging.getLogger(__name__)

@dataclass
class TradeSignal:
    """Class for representing a trade signal."""
    symbol: str
    direction: str  # "BUY" or "SELL"
    entry_price: float
    stop_loss: float
    targets: List[float]
    market_type: str = "SPOT"  # "SPOT" or "FUTURES"
    win_rate: float = 0.0
    timestamp: float = field(default_factory=lambda: datetime.now().timestamp())
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    status: str = "pending"  # pending, entry_triggered, target_hit, stop_loss_hit, closed
    targets_hit: List[int] = field(default_factory=list)  # Indices of targets that have been hit
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the trade signal to a dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, signal_id: str, data: Dict[str, Any]) -> 'TradeSignal':
        """Create a trade signal from a dictionary."""
        return cls(
            id=signal_id,
            symbol=data.get("symbol", ""),
            direction=data.get("direction", ""),
            entry_price=data.get("entry_price", 0.0),
            stop_loss=data.get("stop_loss", 0.0),
            targets=data.get("targets", []),
            timestamp=data.get("timestamp", datetime.now().timestamp()),
            market_type=data.get("market_type", "SPOT"),
            win_rate=data.get("win_rate", 0.0),
            status=data.get("status", "pending"),
            targets_hit=data.get("targets_hit", [])
        )
    
    def format_message(self) -> str:
        """Format the trade signal as an enhanced educational message."""
        # Get proper direction display
        direction_display = self._get_direction_display()

        # Format entry price
        entry_price_str = f"{self.entry_price:.8f}".rstrip('0').rstrip('.')

        # Format stop loss
        stop_loss_str = f"{self.stop_loss:.8f}".rstrip('0').rstrip('.')

        # Format targets with emojis
        targets_str = []
        for i, target in enumerate(self.targets):
            target_str = f"{target:.8f}".rstrip('0').rstrip('.')
            target_emoji = "🥇" if i == 0 else "🥈" if i == 1 else "🥉"
            targets_str.append(f"{target_emoji} Target {i+1}: {target_str}")

        # Format risk/reward ratio
        if self.direction in ["BUY", "LONG"]:
            risk = self.entry_price - self.stop_loss
            rewards = [target - self.entry_price for target in self.targets]
        else:  # SELL/SHORT
            risk = self.stop_loss - self.entry_price
            rewards = [self.entry_price - target for target in self.targets]

        risk_reward_ratios = [f"{reward/risk:.2f}" for reward in rewards if risk > 0]

        # Calculate risk and reward percentages
        risk_percent = abs(risk / self.entry_price * 100) if self.entry_price > 0 else 0
        reward_percent = abs(rewards[0] / self.entry_price * 100) if rewards and self.entry_price > 0 else 0

        # Format enhanced message
        message = f"""
<b>🎯 PROFESSIONAL TRADE SIGNAL</b>

📊 <b>TRADE DETAILS:</b>
<b>Pair:</b> {self.symbol}
<b>Direction:</b> {direction_display}
<b>Market:</b> {self.market_type}

<b>Entry Price:</b> {entry_price_str}
<b>Stop Loss:</b> {stop_loss_str}

🎯 <b>PROFIT TARGETS:</b>
"""

        for i, (target, ratio) in enumerate(zip(targets_str, risk_reward_ratios)):
            message += f"{target} (R/R: {ratio})\n"

        # Add risk management section
        message += f"""
⚠️ <b>RISK MANAGEMENT:</b>
• Risk: {risk_percent:.1f}% | Reward: {reward_percent:.1f}%
• Win Rate: {self.win_rate*100:.1f}%
• Max Position: 1-2% of capital
"""

        # Add educational note
        if self.market_type == "FUTURES":
            message += "\n💡 <b>FUTURES TRADING:</b>\n• Use appropriate leverage (2-5x recommended)\n• Monitor funding rates\n• Set stop loss before entering"
        else:
            message += "\n💡 <b>SPOT TRADING:</b>\n• No leverage risk\n• Hold for targets or cut losses at stop\n• Consider DCA if confident in analysis"

        # Add status information
        if self.status == "entry_triggered":
            message += "\n\n✅ <b>Status:</b> Entry triggered"
        elif self.status.startswith("target_hit"):
            message += "\n\n🎯 <b>Status:</b> Target(s) hit"
            for i in self.targets_hit:
                message += f"\n✅ Target {i+1} achieved"
        elif self.status == "stop_loss_hit":
            message += "\n\n❌ <b>Status:</b> Stop loss hit"
        elif self.status == "closed":
            message += "\n\n✅ <b>Status:</b> Trade completed"

        return message

    def _get_direction_display(self) -> str:
        """Get the appropriate direction display based on market type."""
        if self.market_type == "FUTURES":
            return "LONG 📈" if self.direction == "BUY" else "SHORT 📉"
        else:
            return f"{self.direction} {'📈' if self.direction == 'BUY' else '📉'}"
    
    def update_status(self, new_status: str, target_index: Optional[int] = None) -> None:
        """Update the status of the trade signal."""
        self.status = new_status
        
        if new_status == "target_hit" and target_index is not None:
            if target_index not in self.targets_hit:
                self.targets_hit.append(target_index)
    
    def get_alert_message(self) -> str:
        """Get an alert message based on the current status."""
        if self.status == "entry_triggered":
            return f"🔔 <b>ENTRY TRIGGERED</b> for {self.symbol} {self.direction} trade!"
        elif self.status.startswith("target_hit"):
            target_index = self.targets_hit[-1] if self.targets_hit else 0
            return f"🎯 <b>TARGET {target_index+1} HIT</b> for {self.symbol} {self.direction} trade!"
        elif self.status == "stop_loss_hit":
            return f"⚠️ <b>STOP LOSS HIT</b> for {self.symbol} {self.direction} trade!"
        elif self.status == "closed":
            return f"✅ <b>TRADE CLOSED</b> for {self.symbol} {self.direction} trade!"
        else:
            return f"ℹ️ Status update for {self.symbol} {self.direction} trade: {self.status}"

def get_active_signals() -> Dict[str, Dict[str, Any]]:
    """Get all active trade signals."""
    return get_active_signals()

def save_signal(signal: TradeSignal) -> bool:
    """Save a trade signal."""
    return save_active_signal(signal.id, signal.to_dict())

def update_signal(signal_id: str, signal_data: Dict[str, Any]) -> bool:
    """Update a trade signal."""
    return update_signal_db(signal_id, signal_data)

def get_signal(signal_id: str) -> Optional[TradeSignal]:
    """Get a trade signal by ID."""
    signals = get_active_signals()
    if signal_id in signals:
        return TradeSignal.from_dict(signal_id, signals[signal_id])
    return None
